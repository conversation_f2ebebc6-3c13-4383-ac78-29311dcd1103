'use client';

import { getCurrentUser, fetchAuthSession } from 'aws-amplify/auth';

/**
 * Client-side utility to retrieve Cognito refresh token from localStorage
 * This follows the pattern used in your other application
 */
export async function getCognitoRefreshTokenFromStorage(): Promise<string | null> {
  if (typeof window === 'undefined') {
    console.warn('getCognitoRefreshTokenFromStorage called on server-side');
    return null;
  }

  try {
    // Get current user and auth session to extract client ID and user sub
    const [userDetails, authSession] = await Promise.all([
      getCurrentUser(),
      fetchAuthSession()
    ]);

    const clientId = authSession.tokens?.accessToken?.payload?.client_id;
    let userSub = authSession.userSub || userDetails.userId;

    // Handle Google OAuth users (special case from your other app)
    if (authSession.tokens?.accessToken?.payload?.username?.includes('google')) {
      userSub = authSession.tokens.accessToken.payload.username;
    }

    if (!clientId || !userSub) {
      console.warn('Missing clientId or userSub for refresh token retrieval');
      return null;
    }

    // Retrieve refresh token using Cognito's localStorage pattern
    const refreshTokenKey = `CognitoIdentityServiceProvider.${clientId}.${userSub}.refreshToken`;
    const refreshToken = localStorage.getItem(refreshTokenKey);

    console.log('🔐 [COGNITO] Refresh token retrieval:', {
      clientId: clientId.substring(0, 10) + '...',
      userSub: userSub.substring(0, 10) + '...',
      hasRefreshToken: !!refreshToken,
      refreshTokenLength: refreshToken?.length || 0,
    });

    return refreshToken;
  } catch (error) {
    console.error('🔐 [COGNITO] Error retrieving refresh token from localStorage:', error);
    return null;
  }
}

/**
 * Generate authentication cookies similar to your other app
 * This can be called client-side after successful authentication
 */
export async function generateAuthCookies(): Promise<{
  refreshToken: string | null;
  email: string | null;
} | null> {
  if (typeof window === 'undefined') {
    console.warn('generateAuthCookies called on server-side');
    return null;
  }

  try {
    const [userDetails, authSession] = await Promise.all([
      getCurrentUser(),
      fetchAuthSession()
    ]);

    const clientId = authSession.tokens?.accessToken?.payload?.client_id;
    let userSub = authSession.userSub || userDetails.userId;

    // Handle Google OAuth users
    if (authSession.tokens?.accessToken?.payload?.username?.includes('google')) {
      userSub = authSession.tokens.accessToken.payload.username;
    }

    if (!clientId || !userSub) {
      console.warn('Missing clientId or userSub for cookie generation');
      return null;
    }

    // Get refresh token from localStorage
    const refreshTokenKey = `CognitoIdentityServiceProvider.${clientId}.${userSub}.refreshToken`;
    const refreshToken = localStorage.getItem(refreshTokenKey);

    // Get email from ID token
    const email = authSession.tokens?.idToken?.payload?.email || null;

    console.log('🍪 [COGNITO] Generated auth data:', {
      hasRefreshToken: !!refreshToken,
      email: email,
    });

    return {
      refreshToken,
      email,
    };
  } catch (error) {
    console.error('🍪 [COGNITO] Error generating auth cookies:', error);
    return null;
  }
}

/**
 * Send refresh token to server to store in secure cookies
 * This can be called after successful client-side authentication
 */
export async function storeRefreshTokenInServerCookies(): Promise<boolean> {
  try {
    const authData = await generateAuthCookies();
    
    if (!authData?.refreshToken) {
      console.warn('🍪 [COGNITO] No refresh token available to store');
      return false;
    }

    // Send to server endpoint to store in secure cookies
    const response = await fetch('/api/store-cognito-tokens', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        refreshToken: authData.refreshToken,
        email: authData.email,
      }),
    });

    if (response.ok) {
      console.log('🍪 [COGNITO] Refresh token stored in server cookies');
      return true;
    } else {
      console.error('🍪 [COGNITO] Failed to store refresh token in server cookies');
      return false;
    }
  } catch (error) {
    console.error('🍪 [COGNITO] Error storing refresh token in server cookies:', error);
    return false;
  }
}

/**
 * Get all Cognito tokens from localStorage (for debugging)
 */
export function getAllCognitoTokensFromStorage(): Record<string, string> {
  if (typeof window === 'undefined') {
    return {};
  }

  const cognitoTokens: Record<string, string> = {};
  
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (key && key.startsWith('CognitoIdentityServiceProvider.')) {
      const value = localStorage.getItem(key);
      if (value) {
        cognitoTokens[key] = value;
      }
    }
  }

  return cognitoTokens;
}
