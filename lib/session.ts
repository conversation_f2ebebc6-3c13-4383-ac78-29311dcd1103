// lib/session.ts

'use server';

import { JWTPayload, SignJWT, jwtVerify } from 'jose';
import { cookies } from 'next/headers';
import { randomBytes } from 'crypto';

// Session configuration
const SESSION_SECRET = new TextEncoder().encode(
  process.env.SESSION_SECRET || 'fallback-secret-key-change-in-production',
);
// Use secure cookie names only in production, simple names in development
const SESSION_COOKIE_NAME = process.env.NODE_ENV === 'production' ? '__Secure-session' : 'session';
const CSRF_COOKIE_NAME = process.env.NODE_ENV === 'production' ? '__Host-csrf-token' : 'csrf-token';
const ACCESS_TOKEN_COOKIE_NAME =
  process.env.NODE_ENV === 'production' ? '__Secure-access-token' : 'access-token';
const ID_TOKEN_COOKIE_NAME =
  process.env.NODE_ENV === 'production' ? '__Secure-id-token' : 'id-token';
const SESSION_DURATION = 7 * 24 * 60 * 60 * 1000; // 7 days
const CSRF_TOKEN_LENGTH = 32;

// Session payload interface
interface SessionPayload {
  username: string;
  userId?: string;
  email: string;
  signedIn: boolean;
  iat: number;
  exp: number;
  jti: string; // JWT ID for session tracking
}

// CSRF token interface
interface CSRFToken {
  token: string;
  exp: number;
}

/**
 * Secure cookie configuration with all security flags
 */
const getSecureCookieOptions = (maxAge?: number) => ({
  httpOnly: true,
  secure: process.env.NODE_ENV === 'production',
  sameSite: 'strict' as const,
  maxAge: maxAge || SESSION_DURATION / 1000,
  path: '/',
  ...(process.env.NODE_ENV === 'production' && {
    domain: process.env.COOKIE_DOMAIN, // Set your domain in production
  }),
});

/**
 * Generate a cryptographically secure random token
 */
function generateSecureToken(length: number = CSRF_TOKEN_LENGTH): string {
  return randomBytes(length).toString('hex');
}

/**
 * Create a signed JWT session token
 */
export async function createSession(userData: {
  username: string;
  email: string;
  userId?: string;
}): Promise<string> {
  const now = Date.now();
  const exp = now + SESSION_DURATION;
  const jti = generateSecureToken(16); // Unique session ID

  const payload: SessionPayload = {
    username: userData.username,
    email: userData.email,
    userId: userData.userId,
    signedIn: true,
    iat: Math.floor(now / 1000),
    exp: Math.floor(exp / 1000),
    jti,
  };

  return await new SignJWT(payload as unknown as JWTPayload)
    .setProtectedHeader({ alg: 'HS256' })
    .setIssuedAt()
    .setExpirationTime(payload.exp)
    .setJti(jti)
    .sign(SESSION_SECRET);
}

/**
 * Verify and decode a session token
 */
export async function verifySession(token: string): Promise<SessionPayload | null> {
  try {
    const { payload } = await jwtVerify(token, SESSION_SECRET);

    // Validate payload structure
    if (
      typeof payload.username === 'string' &&
      typeof payload.email === 'string' &&
      typeof payload.signedIn === 'boolean' &&
      typeof payload.jti === 'string' &&
      payload.signedIn === true
    ) {
      return payload as unknown as SessionPayload;
    }

    return null;
  } catch (error) {
    console.error('Session verification failed:', error);
    return null;
  }
}

/**
 * Get current session from cookies
 */
export async function getSession(): Promise<SessionPayload | null> {
  try {
    const cookieStore = await cookies();
    const sessionCookie = cookieStore.get(SESSION_COOKIE_NAME);

    if (!sessionCookie?.value) {
      return null;
    }

    return await verifySession(sessionCookie.value);
  } catch (error) {
    console.error('Session retrieval error:', error);
    return null;
  }
}

/**
 * Set session cookie with secure configuration
 */
export async function setSessionCookie(sessionToken: string): Promise<void> {
  const cookieStore = await cookies();

  cookieStore.set(SESSION_COOKIE_NAME, sessionToken, {
    ...getSecureCookieOptions(),
  });
}

/**
 * Generate and set CSRF token
 */
export async function generateCSRFToken(): Promise<string> {
  const token = generateSecureToken();
  const exp = Date.now() + 60 * 60 * 1000; // 1 hour expiry

  const csrfData: CSRFToken = { token, exp };
  const cookieStore = await cookies();

  cookieStore.set(CSRF_COOKIE_NAME, JSON.stringify(csrfData), {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict',
    maxAge: 60 * 60, // 1 hour
    path: '/',
  });

  return token;
}

/**
 * Verify CSRF token from request
 */
export async function verifyCSRFToken(providedToken: string): Promise<boolean> {
  try {
    const cookieStore = await cookies();
    const csrfCookie = cookieStore.get(CSRF_COOKIE_NAME);

    if (!csrfCookie?.value || !providedToken) {
      return false;
    }

    const csrfData: CSRFToken = JSON.parse(csrfCookie.value);

    // Check token match and expiry
    if (csrfData.token === providedToken && Date.now() < csrfData.exp) {
      return true;
    }

    return false;
  } catch (error) {
    console.error('CSRF token verification failed:', error);
    return false;
  }
}

/**
 * Clear all session and security cookies
 */
export async function clearSession(): Promise<void> {
  const cookieStore = await cookies();

  console.log('🧹 [SESSION] Clearing all session and token cookies...');

  // Clear session cookie
  cookieStore.delete(SESSION_COOKIE_NAME);

  // Clear CSRF token
  cookieStore.delete(CSRF_COOKIE_NAME);

  // Clear Cognito token cookies
  cookieStore.delete(ACCESS_TOKEN_COOKIE_NAME);
  cookieStore.delete(ID_TOKEN_COOKIE_NAME);

  // Clear any legacy cookies
  cookieStore.delete('amplify-auth-token');
  cookieStore.delete('temp-signup-password');

  console.log('🧹 [SESSION] All cookies cleared');
}

/**
 * Refresh session if needed (extend expiry)
 */
export async function refreshSession(currentSession: SessionPayload): Promise<string | null> {
  // Refresh if session will expire in next 24 hours
  const timeUntilExpiry = currentSession.exp * 1000 - Date.now();
  const shouldRefresh = timeUntilExpiry < 24 * 60 * 60 * 1000;

  if (shouldRefresh) {
    return await createSession({
      username: currentSession.username,
      email: currentSession.email,
      userId: currentSession.userId,
    });
  }

  return null;
}

/**
 * Validate session and optionally refresh
 */
export async function validateAndRefreshSession(): Promise<SessionPayload | null> {
  const session = await getSession();

  if (!session) {
    return null;
  }

  // Try to refresh if needed
  const newToken = await refreshSession(session);
  if (newToken) {
    await setSessionCookie(newToken);
    // Return updated session data
    return await verifySession(newToken);
  }

  return session;
}

/**
 * Set Cognito tokens in secure HTTP-only cookies
 */
export async function setCognitoTokenCookies(tokens: {
  accessToken?: string;
  idToken?: string;
}): Promise<void> {
  const cookieStore = await cookies();
  const cookieOptions = {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict' as const,
    maxAge: 60 * 60, // 1 hour for access/id tokens
    path: '/',
  };

  console.log('🍪 [TOKENS] Setting Cognito token cookies...');

  if (tokens.accessToken) {
    cookieStore.set(ACCESS_TOKEN_COOKIE_NAME, tokens.accessToken, cookieOptions);
    console.log('🍪 [TOKENS] Access token cookie set');
  }

  if (tokens.idToken) {
    cookieStore.set(ID_TOKEN_COOKIE_NAME, tokens.idToken, cookieOptions);
    console.log('🍪 [TOKENS] ID token cookie set');
  }
}

/**
 * Get Cognito tokens from cookies
 */
export async function getCognitoTokenCookies(): Promise<{
  accessToken?: string;
  idToken?: string;
}> {
  try {
    const cookieStore = await cookies();

    const accessToken = cookieStore.get(ACCESS_TOKEN_COOKIE_NAME)?.value;
    const idToken = cookieStore.get(ID_TOKEN_COOKIE_NAME)?.value;

    console.log('🍪 [TOKENS] Retrieved Cognito tokens from cookies:', {
      hasAccessToken: !!accessToken,
      hasIdToken: !!idToken,
    });

    return {
      accessToken,
      idToken,
    };
  } catch (error) {
    console.error('🍪 [TOKENS] Error retrieving Cognito tokens from cookies:', error);
    return {};
  }
}

/**
 * Clear Cognito token cookies
 */
export async function clearCognitoTokenCookies(): Promise<void> {
  const cookieStore = await cookies();

  console.log('🍪 [TOKENS] Clearing Cognito token cookies...');

  cookieStore.delete(ACCESS_TOKEN_COOKIE_NAME);
  cookieStore.delete(ID_TOKEN_COOKIE_NAME);

  console.log('🍪 [TOKENS] Cognito token cookies cleared');
}

/**
 * Get user email from ID token cookie
 */
export async function getUserEmailFromToken(): Promise<string | null> {
  try {
    const tokens = await getCognitoTokenCookies();

    if (tokens.idToken) {
      // Decode JWT payload (note: this is not secure validation, just for extracting email)
      const payload = JSON.parse(atob(tokens.idToken.split('.')[1]));
      return payload.email || null;
    }

    return null;
  } catch (error) {
    console.error('🍪 [TOKENS] Error extracting email from ID token:', error);
    return null;
  }
}
