import axios, { AxiosResponse, AxiosError } from 'axios';
import type { InternalAxiosRequestConfig } from 'axios';
import { getCognitoTokenCookies, getUserEmailFromToken } from '@/lib/session';

// Determine base URL based on environment
const env = process.env.NEXT_PUBLIC_NODE_ENV;
const baseURL =
  env === 'production'
    ? 'https://api.qbraid.com/api'
    : env === 'staging'
      ? 'https://api-staging-1.qbraid.com/api'
      : process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api';

// Create axios instance
const client = axios.create({
  baseURL,
  headers: { 'Content-Type': 'application/json' },
});

// Request interceptor to add auth headers
client.interceptors.request.use(async (config: InternalAxiosRequestConfig) => {
  config.headers = config.headers || {};

  try {
    // Try to get tokens from secure cookies first
    const tokens = await getCognitoTokenCookies();
    const userEmail = await getUserEmailFromToken();

    // Priority 1: Use access token for authorization
    if (tokens.accessToken) {
      config.headers['authorization'] = `Bearer ${tokens.accessToken}`;
      console.log('🔐 [API] Using access token from secure cookie');
    }

    // Priority 2: Use refresh token for API calls that need it
    if (tokens.refreshToken) {
      config.headers['refresh-token'] = tokens.refreshToken;
      console.log('🔐 [API] Using refresh token from secure cookie');
    }

    // Priority 3: Use email from ID token
    if (userEmail) {
      config.headers['email'] = userEmail;
      console.log('🔐 [API] Using email from ID token cookie');
    }

    // Fallback to environment variables if no cookies available
    if (!tokens.accessToken && !tokens.refreshToken && !userEmail) {
      console.log('🔐 [API] No tokens in cookies, falling back to environment variables');

      if (process.env.NEXT_PUBLIC_EMAIL) {
        config.headers['email'] = process.env.NEXT_PUBLIC_EMAIL;
      }
      if (process.env.NEXT_PUBLIC_REFRESH_TOKEN) {
        config.headers['refresh-token'] = process.env.NEXT_PUBLIC_REFRESH_TOKEN;
      }
    }

    // Log final headers (without sensitive data)
    console.log('🔐 [API] Request headers configured:', {
      hasAuthorization: !!config.headers['authorization'],
      hasRefreshToken: !!config.headers['refresh-token'],
      hasEmail: !!config.headers['email'],
      url: config.url,
    });
  } catch (error) {
    console.error('🔐 [API] Error retrieving tokens from cookies:', error);

    // Fallback to environment variables on error
    if (process.env.NEXT_PUBLIC_EMAIL) {
      config.headers['email'] = process.env.NEXT_PUBLIC_EMAIL;
    }
    if (process.env.NEXT_PUBLIC_REFRESH_TOKEN) {
      config.headers['refresh-token'] = process.env.NEXT_PUBLIC_REFRESH_TOKEN;
    }
  }

  return config;
});

// Response interceptor for global error handling
client.interceptors.response.use(
  (response: AxiosResponse) => response,
  (error: AxiosError) => {
    console.error('API Error:', error.response?.status, error.message);
    return Promise.reject(error);
  },
);

export default client;
