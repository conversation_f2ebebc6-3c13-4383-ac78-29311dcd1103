// app/auth/actions.ts
'use server';

import {
  signIn,
  signUp,
  confirmSignUp,
  resetPassword,
  confirmResetPassword,
  signOut as amplifySignOut,
  fetchAuthSession,
  getCurrentUser,
  AuthTokens,
} from '@aws-amplify/auth';
import { Amplify } from 'aws-amplify';

import { redirect } from 'next/navigation';
import {
  createSession,
  setSessionCookie,
  clearSession,
  generateCSRFToken,
  verifyCSRFToken,
  getSession as getSecureSession,
  setCognitoTokenCookies,
} from '@/lib/session';

/**
 * Custom token storage for server-side Amplify operations
 * This stores tokens in memory temporarily during the authentication process
 */
class ServerSideTokenStorage {
  private tokens: Map<string, any> = new Map();

  async setItem(key: string, value: string): Promise<void> {
    console.log(`🔐 [TOKEN-STORAGE] Setting token: ${key}`);
    this.tokens.set(key, value);
  }

  async getItem(key: string): Promise<string | null> {
    const value = this.tokens.get(key) || null;
    console.log(`🔐 [TOKEN-STORAGE] Getting token: ${key} - ${value ? 'found' : 'not found'}`);
    return value;
  }

  async removeItem(key: string): Promise<void> {
    console.log(`🔐 [TOKEN-STORAGE] Removing token: ${key}`);
    this.tokens.delete(key);
  }

  async clear(): Promise<void> {
    console.log(`🔐 [TOKEN-STORAGE] Clearing all tokens`);
    this.tokens.clear();
  }
}

const serverTokenStorage = new ServerSideTokenStorage();

/**
 * Ensures Amplify is properly configured for server-side operations
 *
 * This function configures Amplify with Cognito settings and custom token storage
 * to prevent "Amplify has not been configured" errors in server actions.
 */
const ensureAmplifyConfig = () => {
  const config = {
    Auth: {
      Cognito: {
        userPoolId: process.env.NEXT_PUBLIC_COGNITO_USER_POOL_ID!,
        userPoolClientId: process.env.NEXT_PUBLIC_COGNITO_CLIENT_ID!,
        signUpVerificationMethod: 'code',
        loginWith: {
          email: true,
          username: false,
          phone: false,
        },
      },
    },
    Storage: serverTokenStorage,
  };

  try {
    console.log('🔧 [AUTH] Configuring Amplify for server-side with custom token storage...');
    // Configure without SSR to avoid token persistence issues
    Amplify.configure(config as any);
  } catch (error) {
    console.error('Failed to configure Amplify:', error);
    // Try configuring without SSR option as fallback
    try {
      Amplify.configure(config as any);
    } catch (fallbackError) {
      console.error('Fallback Amplify configuration failed:', fallbackError);
    }
  }
};

/**
 * Standard return type for all authentication operations
 */
interface AuthResult {
  success: boolean;
  error?: string;
  requiresVerification?: boolean;
  nextStep?: string;
  redirectTo?: string;
  email?: string;
  csrfToken?: string;
}

/**
 * Validates CSRF token for state-changing operations
 */
async function validateCSRFToken(formData: FormData): Promise<boolean> {
  const csrfToken = formData.get('csrfToken') as string;
  if (!csrfToken) {
    console.error('CSRF token missing from form submission');
    return false;
  }
  return await verifyCSRFToken(csrfToken);
}

/**
 * Authenticates a user with email and password
 *
 * This server action handles user sign-in with comprehensive error handling.
 * It creates a secure HTTP-only cookie session on successful authentication.
 *
 * @param prevState - Previous form state from useActionState
 * @param formData - Form data containing email and password
 * @returns Promise<AuthResult> - Authentication result with success/error info
 */
export async function authenticateUser(prevState: any, formData: FormData): Promise<AuthResult> {
  const email = formData.get('email') as string;
  const password = formData.get('password') as string;

  try {
    // Ensure Amplify is configured before any auth operations
    ensureAmplifyConfig();

    // Validate CSRF token for security
    const isCSRFValid = await validateCSRFToken(formData);
    if (!isCSRFValid) {
      return { success: false, error: 'Invalid request. Please try again.' };
    }

    // Basic input validation
    if (!email || !password) {
      return { success: false, error: 'Email and password are required' };
    }

    console.log('🔐 [AUTH] Attempting sign in for:', email);
    const result = await signIn({ username: email, password });
    console.debug(result);
    console.log('✅ [AUTH] Sign in result:', {
      isSignedIn: result.isSignedIn,
      nextStep: result.nextStep,
    });

    if (result.isSignedIn) {
      console.log('🎫 [AUTH] Sign in successful, getting user details and session...');

      // Get user details and session tokens
      let userId = email; // fallback
      let tokens = null;

      try {
        // Get current user details
        const userDetails = await getCurrentUser();
        userId = userDetails.userId || userDetails.username || email;
        console.log('👤 [AUTH] User details:', {
          userId: userId,
          username: userDetails.username,
          email: email,
        });
        console.debug('--------------------------------');
        console.debug('USERDETAILS::::\n', userDetails);
        console.debug('--------------------------------');
        // Get auth session with tokens
        const authSession = await fetchAuthSession();
        tokens = authSession.tokens as AuthTokens;
        console.debug('--------------------------------');
        console.debug('Full AuthSession object:', JSON.stringify(authSession, null, 2));
        console.debug('AuthSession credentials:', authSession.credentials);
        console.debug('Authsession:::: Access Token', authSession.tokens?.accessToken);
        console.debug('Authsession:::: ID Token', authSession.tokens?.idToken);
        console.debug('--------------------------------');
        console.log('🎫 [AUTH] Auth session tokens:', {
          hasAccessToken: !!tokens?.accessToken,
          hasIdToken: !!tokens?.idToken,
          accessTokenLength: tokens?.accessToken?.toString().length || 0,
          idTokenLength: tokens?.idToken?.toString().length || 0,
        });

        // Store Cognito tokens in secure cookies for API calls
        if (tokens) {
          // Extract refresh token from credentials if available
          const refreshToken = authSession.credentials
            ? (authSession.credentials as any)?.refreshToken?.toString()
            : undefined;

          await setCognitoTokenCookies({
            accessToken: tokens.accessToken?.toString(),
            idToken: tokens.idToken?.toString(),
            refreshToken: refreshToken,
          });

          console.log('🍪 [AUTH] Cognito tokens stored in secure cookies:', {
            hasAccessToken: !!tokens.accessToken,
            hasIdToken: !!tokens.idToken,
            hasRefreshToken: !!refreshToken,
          });
        }
      } catch (sessionError) {
        console.warn('⚠️ [AUTH] Could not get user details or tokens:', sessionError);
        // Continue with email as userId
      }

      // Create secure JWT session with proper userId
      const sessionToken = await createSession({
        username: email,
        email: email,
        userId: userId,
      });

      // Set secure session cookie
      await setSessionCookie(sessionToken);

      console.log('✅ [AUTH] Session created and cookie set successfully');

      return { success: true, redirectTo: '/' };
    }

    // Handle multi-step authentication flows (e.g., unverified users)
    // Use generic messages to prevent information disclosure
    if (result.nextStep) {
      switch (result.nextStep.signInStep) {
        case 'CONFIRM_SIGN_UP':
          return {
            success: false,
            requiresVerification: true,
            error: 'Invalid email or password',
          };
        case 'RESET_PASSWORD':
          return {
            success: false,
            error: 'Invalid email or password',
          };
        default:
          return {
            success: false,
            error: 'Authentication failed. Please try again.',
          };
      }
    }

    return { success: false, error: 'Authentication failed' };
  } catch (error: any) {
    console.error('Authentication error:', error);

    // Map Cognito error types to generic user-friendly messages
    // Use generic messages to prevent information disclosure about registered emails
    switch (error.name) {
      case 'UserNotConfirmedException':
        return {
          success: false,
          requiresVerification: true,
          error: 'Invalid email or password',
        };
      case 'NotAuthorizedException':
        return { success: false, error: 'Invalid email or password' };
      case 'UserNotFoundException':
        return { success: false, error: 'Invalid email or password' };
      case 'PasswordResetRequiredException':
        return { success: false, error: 'Invalid email or password' };
      case 'TooManyRequestsException':
        return { success: false, error: 'Too many attempts. Please try again later.' };
      case 'UnexpectedSignInInterruptionException':
        console.log(
          '🔧 [AUTH] Handling UnexpectedSignInInterruptionException - attempting recovery...',
        );

        // Special case: Sometimes Cognito throws this but user is actually signed in
        // Try to get user details and create session anyway
        let recoveredUserId = email; // fallback

        try {
          const userDetails = await getCurrentUser();
          recoveredUserId = userDetails.userId || userDetails.username || email;
          console.log('✅ [AUTH] Successfully recovered user details:', {
            userId: recoveredUserId,
            username: userDetails.username,
          });
        } catch (recoveryError) {
          console.warn('⚠️ [AUTH] Could not recover user details, using email as userId');
        }

        // Create secure JWT session with recovered userId
        const sessionToken = await createSession({
          username: email,
          email: email,
          userId: recoveredUserId,
        });
        await setSessionCookie(sessionToken);

        console.log('✅ [AUTH] Successfully recovered from UnexpectedSignInInterruptionException');
        return { success: true, redirectTo: '/' };
      default:
        return {
          success: false,
          error: 'Authentication failed. Please try again.',
        };
    }
  }
}

/**
 * Registers a new user account
 *
 * Creates a new user in Cognito with email verification required.
 * Validates input data and handles registration errors appropriately.
 *
 * @param prevState - Previous form state from useActionState
 * @param formData - Form data containing name, email, password, confirmPassword
 * @returns Promise<AuthResult> - Registration result with next steps
 */
export async function registerUser(prevState: any, formData: FormData): Promise<AuthResult> {
  const name = formData.get('name') as string;
  const email = formData.get('email') as string;
  const password = formData.get('password') as string;
  const confirmPassword = formData.get('confirmPassword') as string;

  try {
    ensureAmplifyConfig();

    // Validate CSRF token for security
    const isCSRFValid = await validateCSRFToken(formData);
    if (!isCSRFValid) {
      return { success: false, error: 'Invalid request. Please try again.' };
    }

    // Validate required fields
    if (!name || !email || !password) {
      return { success: false, error: 'Name, email and password are required' };
    }

    // Validate password confirmation
    if (password !== confirmPassword) {
      return { success: false, error: 'Passwords do not match' };
    }

    // Basic password strength validation
    if (password.length < 8) {
      return { success: false, error: 'Password must be at least 8 characters long' };
    }

    console.log('Attempting sign up for:', email);
    const result = await signUp({
      username: email, // Use email as username
      password,
      options: {
        userAttributes: {
          email,
          name,
          preferred_username: email,
        },
      },
    });

    console.log('Sign up result:', result.nextStep);

    // Most sign-ups require email verification
    if (result.nextStep?.signUpStep === 'CONFIRM_SIGN_UP') {
      return {
        success: true,
        nextStep: 'verify',
        email: email,
        error: 'Please check your email for verification code',
      };
    }

    return { success: true };
  } catch (error: any) {
    console.error('Registration error:', error);

    // Handle common registration errors
    switch (error.name) {
      case 'UsernameExistsException':
        return { success: false, error: 'An account with this email already exists' };
      case 'InvalidPasswordException':
        return { success: false, error: 'Password does not meet requirements' };
      case 'InvalidParameterException':
        return { success: false, error: 'Invalid email format' };
      default:
        return {
          success: false,
          error: 'Registration failed. Please try again.',
        };
    }
  }
}

/**
 * Verifies user email with confirmation code
 *
 * Completes the user registration process by confirming the email
 * with the verification code sent by Cognito.
 *
 * @param prevState - Previous form state from useActionState
 * @param formData - Form data containing email and verification code
 * @returns Promise<AuthResult> - Verification result
 */
export async function verifyEmail(prevState: any, formData: FormData): Promise<AuthResult> {
  try {
    ensureAmplifyConfig();

    // Validate CSRF token for security
    const isCSRFValid = await validateCSRFToken(formData);
    if (!isCSRFValid) {
      return { success: false, error: 'Invalid request. Please try again.' };
    }

    const email = formData.get('email') as string;
    const code = formData.get('code') as string;

    // Validate required inputs
    if (!email || !code) {
      return { success: false, error: 'Email and verification code are required' };
    }

    console.log('Attempting email verification for:', email, 'with code:', code);
    await confirmSignUp({
      username: email,
      confirmationCode: code,
    });

    console.log('Email verification successful for:', email);

    return { success: true };
  } catch (error: any) {
    console.error('Verification error:', error);

    // Handle verification-specific errors
    switch (error.name) {
      case 'CodeMismatchException':
        return { success: false, error: 'Invalid verification code' };
      case 'ExpiredCodeException':
        return { success: false, error: 'Verification code has expired' };
      case 'NotAuthorizedException':
        return { success: false, error: 'User is already verified' };
      case 'AliasExistsException':
        // User is already confirmed, treat as success
        return { success: true };
      default:
        return {
          success: false,
          error: 'Verification failed. Please try again.',
        };
    }
  }
}

/**
 * Initiates password reset process
 *
 * Sends a password reset code to the user's email address.
 * Used for "forgot password" functionality.
 *
 * @param prevState - Previous form state from useActionState
 * @param formData - Form data containing email address
 * @returns Promise<AuthResult> - Reset initiation result
 */
export async function initiatePasswordReset(
  prevState: any,
  formData: FormData,
): Promise<AuthResult> {
  try {
    ensureAmplifyConfig();

    // Validate CSRF token for security
    const isCSRFValid = await validateCSRFToken(formData);
    if (!isCSRFValid) {
      return { success: false, error: 'Invalid request. Please try again.' };
    }

    const email = formData.get('email') as string;

    if (!email) {
      return { success: false, error: 'Email is required' };
    }

    await resetPassword({ username: email });
    return {
      success: true,
      nextStep: 'reset',
      error: 'Password reset code sent to your email',
    };
  } catch (error: any) {
    console.error('Password reset error:', error);

    switch (error.name) {
      case 'UserNotFoundException':
        return { success: false, error: 'No account found with this email' };
      case 'NotAuthorizedException':
        return { success: false, error: 'User account is disabled' };
      default:
        return {
          success: false,
          error: 'Failed to send reset code. Please try again.',
        };
    }
  }
}

/**
 * Completes password reset with new password
 *
 * Uses the reset code sent via email to set a new password.
 * Validates password confirmation and strength requirements.
 *
 * @param prevState - Previous form state from useActionState
 * @param formData - Form data containing email, code, and new passwords
 * @returns Promise<AuthResult> - Reset completion result
 */
export async function completePasswordReset(
  prevState: any,
  formData: FormData,
): Promise<AuthResult> {
  try {
    ensureAmplifyConfig();

    // Validate CSRF token for security
    const isCSRFValid = await validateCSRFToken(formData);
    if (!isCSRFValid) {
      return { success: false, error: 'Invalid request. Please try again.' };
    }

    const email = formData.get('email') as string;
    const code = formData.get('code') as string;
    const newPassword = formData.get('newPassword') as string;
    const confirmPassword = formData.get('confirmPassword') as string;

    // Validate all required fields
    if (!email || !code || !newPassword) {
      return { success: false, error: 'All fields are required' };
    }

    // Validate password confirmation
    if (newPassword !== confirmPassword) {
      return { success: false, error: 'Passwords do not match' };
    }

    // Basic password strength validation
    if (newPassword.length < 8) {
      return { success: false, error: 'Password must be at least 8 characters long' };
    }

    await confirmResetPassword({
      username: email,
      confirmationCode: code,
      newPassword,
    });

    return { success: true };
  } catch (error: any) {
    console.error('Password reset completion error:', error);

    switch (error.name) {
      case 'CodeMismatchException':
        return { success: false, error: 'Invalid reset code' };
      case 'ExpiredCodeException':
        return { success: false, error: 'Reset code has expired' };
      case 'InvalidPasswordException':
        return { success: false, error: 'Password does not meet requirements' };
      default:
        return {
          success: false,
          error: 'Password reset failed. Please try again.',
        };
    }
  }
}

/**
 * Logs out the current user
 *
 * Performs Amplify sign out and cleans up the session cookie.
 * Redirects to sign-in page after successful logout.
 *
 * Note: This function always redirects, so it doesn't return a value.
 */
export async function logout() {
  try {
    ensureAmplifyConfig();
    // Sign out from Cognito
    await amplifySignOut();
  } catch (error) {
    console.error('Logout error:', error);
    // Continue with cleanup even if Amplify logout fails
  } finally {
    // Clear all session and security cookies
    await clearSession();
    redirect('/auth/signin');
  }
}

/**
 * Retrieves the current user session using secure JWT tokens
 *
 * Verifies the JWT session token and returns validated session data.
 * Used for server-side authentication checks.
 *
 * @returns Promise<object | null> - Session data or null if not authenticated
 */
export async function getSession() {
  return await getSecureSession();
}

/**
 * Generates a CSRF token for form protection
 *
 * Creates a new CSRF token and returns it for inclusion in forms.
 * Used to protect against Cross-Site Request Forgery attacks.
 *
 * @returns Promise<string> - CSRF token
 */
export async function getCSRFToken(): Promise<string> {
  return await generateCSRFToken();
}
// https://repost.aws/questions/QUpiFs2wUGSD6K9_XcsChHgA/regarding-aws-cognito-service
//https://docs.amplify.aws/angular/build-a-backend/auth/connect-your-frontend/manage-user-sessions/#retrieve-a-user-session
// --------------------------------
// Authsession:::: Access Token {
//   toString: [Function: toString],
//   payload: {
//     sub: 'd12b35d0-1061-70c8-5829-ddf045f5e8e0',
//     iss: 'https://cognito-idp.us-east-2.amazonaws.com/us-east-2_bHQV6YVnt',
//     client_id: '3aju3epc50421b4fp0bh75oj4u',
//     origin_jti: '45fbd033-cd35-4273-937c-240216cda73f',
//     event_id: 'b3335653-ce31-4519-9c40-1c4d49493432',
//     token_use: 'access',
//     scope: 'aws.cognito.signin.user.admin',
//     auth_time: 1751677641,
//     exp: 1751681241,
//     iat: 1751677641,
//     jti: 'cf65f202-2004-4acd-bb3e-2717c7c89063',
//     username: 'd12b35d0-1061-70c8-5829-ddf045f5e8e0'
//   }
// }
// Authsession:::: ID Token {
//   toString: [Function: toString],
//   payload: {
//     sub: 'd12b35d0-1061-70c8-5829-ddf045f5e8e0',
//     email_verified: true,
//     iss: 'https://cognito-idp.us-east-2.amazonaws.com/us-east-2_bHQV6YVnt',
//     'cognito:username': 'd12b35d0-1061-70c8-5829-ddf045f5e8e0',
//     preferred_username: '<EMAIL>',
//     origin_jti: '45fbd033-cd35-4273-937c-240216cda73f',
//     aud: '3aju3epc50421b4fp0bh75oj4u',
//     event_id: 'b3335653-ce31-4519-9c40-1c4d49493432',
//     token_use: 'id',
//     auth_time: 1751677641,
//     name: 'Dhruv',
//     exp: 1751681241,
//     iat: 1751677641,
//     jti: 'f467e2be-9426-495e-b445-9a81d10ffef6',
//     email: '<EMAIL>'
//   }
// }
